// 独立的MCP服务器可执行文件，用于Claude Desktop集成
use tauri_app_lib::run_mcp_server_standalone;
use tracing::{info, error};

#[tokio::main]
async fn main() {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    info!("启动独立MCP服务器...");
    
    // 设置优雅关闭
    let (shutdown_tx, mut shutdown_rx) = tokio::sync::mpsc::unbounded_channel::<()>();
    
    // 监听Ctrl+C信号
    let shutdown_tx_clone = shutdown_tx.clone();
    tokio::spawn(async move {
        if let Err(e) = tokio::signal::ctrl_c().await {
            error!("监听Ctrl+C信号失败: {}", e);
            return;
        }
        info!("收到Ctrl+C信号，准备关闭服务器...");
        let _ = shutdown_tx_clone.send(());
    });
    
    // 启动MCP服务器
    let server_task = tokio::spawn(async move {
        if let Err(e) = run_mcp_server_standalone().await {
            error!("MCP服务器运行错误: {}", e);
        }
    });
    
    // 等待关闭信号或服务器任务完成
    tokio::select! {
        _ = shutdown_rx.recv() => {
            info!("收到关闭信号");
        }
        result = server_task => {
            match result {
                Ok(_) => info!("MCP服务器任务正常结束"),
                Err(e) => error!("MCP服务器任务错误: {}", e),
            }
        }
    }
    
    info!("MCP服务器已关闭");
}
