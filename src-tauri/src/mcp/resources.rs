use turbomcp::prelude::*;
use serde_json::Value;
use std::path::Path;
use tokio::fs;
use tracing::{info, error};

/// 简化的资源管理器
pub struct ResourceManager {
    /// 是否启用安全模式
    secure_mode: bool,
}

impl ResourceManager {
    /// 创建新的资源管理器
    pub fn new(secure_mode: bool) -> Self {
        Self { secure_mode }
    }

    /// 检查路径是否安全
    fn is_path_safe(&self, path: &Path) -> bool {
        if !self.secure_mode {
            return true;
        }

        // 检查路径是否包含危险字符
        let path_str = path.to_string_lossy();
        !path_str.contains("..") && !path_str.contains("~") && !path_str.starts_with("/")
    }

    /// 读取文件资源
    pub async fn read_file_resource(&self, uri: &str) -> McpResult<String> {
        // 解析URI，提取文件路径
        let path = if uri.starts_with("file://") {
            &uri[7..] // 移除 "file://" 前缀
        } else {
            uri
        };

        let file_path = Path::new(path);

        // 安全检查
        if !self.is_path_safe(file_path) {
            return Err(McpError::InvalidRequest(
                format!("不允许访问的路径: {}", path)
            ));
        }

        // 读取文件内容
        match fs::read_to_string(file_path).await {
            Ok(content) => {
                info!("成功读取文件: {}", path);
                Ok(content)
            }
            Err(e) => {
                error!("读取文件失败 {}: {}", path, e);
                Err(McpError::InternalError(
                    format!("读取文件失败: {}", e)
                ))
            }
        }
    }

    /// 处理资源请求
    pub async fn handle_resource(&self, uri: &str) -> McpResult<Value> {
        if uri.starts_with("file://") {
            // 文件资源
            let content = self.read_file_resource(uri).await?;
            Ok(serde_json::json!({
                "uri": uri,
                "type": "file",
                "content": content
            }))
        } else {
            Err(McpError::InvalidRequest(
                format!("不支持的资源URI格式: {}", uri)
            ))
        }
    }
}

impl Default for ResourceManager {
    fn default() -> Self {
        Self::new(true) // 默认启用安全模式
    }
}
