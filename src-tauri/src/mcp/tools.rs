use turbomcp::prelude::*;
use serde_json::Value;

/// 简化的工具管理器
pub struct ToolManager;

impl ToolManager {
    pub fn new() -> Self {
        Self
    }

    /// 获取所有工具列表
    pub fn list_tools(&self) -> Vec<String> {
        vec!["calculator".to_string(), "text_processor".to_string()]
    }

    /// 执行工具
    pub async fn execute_tool(&self, name: &str, args: Value) -> McpResult<Value> {
        match name {
            "calculator" => {
                let calc = CalculatorTool;
                calc.execute(args).await
            }
            "text_processor" => {
                let processor = TextProcessorTool;
                processor.execute(args).await
            }
            _ => Err(McpError::invalid_request(
                format!("未找到工具: {}", name)
            ))
        }
    }
}

/// 工具特征
pub trait Tool: Send + Sync {
    /// 工具名称
    fn name(&self) -> &str;
    /// 工具描述
    fn description(&self) -> &str;
    /// 执行工具
    async fn execute(&self, args: Value) -> McpResult<Value>;
}

/// 示例：计算器工具
#[derive(Clone)]
pub struct CalculatorTool;

impl Tool for CalculatorTool {
    fn name(&self) -> &str {
        "calculator"
    }

    fn description(&self) -> &str {
        "执行基本数学计算"
    }

    async fn execute(&self, args: Value) -> McpResult<Value> {
        let operation = args.get("operation")
            .and_then(|v| v.as_str())
            .ok_or_else(|| McpError::invalid_request("缺少operation参数"))?;

        let a = args.get("a")
            .and_then(|v| v.as_f64())
            .ok_or_else(|| McpError::invalid_request("缺少参数a"))?;

        let b = args.get("b")
            .and_then(|v| v.as_f64())
            .ok_or_else(|| McpError::invalid_request("缺少参数b"))?;

        let result = match operation {
            "add" => a + b,
            "subtract" => a - b,
            "multiply" => a * b,
            "divide" => {
                if b == 0.0 {
                    return Err(McpError::invalid_request("除数不能为零"));
                }
                a / b
            }
            _ => return Err(McpError::invalid_request(
                format!("不支持的操作: {}", operation)
            ))
        };

        Ok(serde_json::json!({
            "result": result,
            "operation": operation,
            "operands": [a, b]
        }))
    }
}

/// 示例：文本处理工具
#[derive(Clone)]
pub struct TextProcessorTool;

impl Tool for TextProcessorTool {
    fn name(&self) -> &str {
        "text_processor"
    }

    fn description(&self) -> &str {
        "处理文本内容，支持大小写转换、长度计算等"
    }

    async fn execute(&self, args: Value) -> McpResult<Value> {
        let text = args.get("text")
            .and_then(|v| v.as_str())
            .ok_or_else(|| McpError::invalid_request("缺少text参数"))?;

        let operation = args.get("operation")
            .and_then(|v| v.as_str())
            .unwrap_or("info");

        let result = match operation {
            "uppercase" => serde_json::json!({
                "original": text,
                "result": text.to_uppercase(),
                "operation": "uppercase"
            }),
            "lowercase" => serde_json::json!({
                "original": text,
                "result": text.to_lowercase(),
                "operation": "lowercase"
            }),
            "length" => serde_json::json!({
                "text": text,
                "length": text.len(),
                "char_count": text.chars().count(),
                "operation": "length"
            }),
            "reverse" => serde_json::json!({
                "original": text,
                "result": text.chars().rev().collect::<String>(),
                "operation": "reverse"
            }),
            "info" => serde_json::json!({
                "text": text,
                "length": text.len(),
                "char_count": text.chars().count(),
                "word_count": text.split_whitespace().count(),
                "operation": "info"
            }),
            _ => return Err(McpError::InvalidRequest(
                format!("不支持的文本操作: {}", operation)
            ))
        };

        Ok(result)
    }
}

impl Default for ToolManager {
    fn default() -> Self {
        Self::new()
    }
}
