use turbomcp::prelude::*;
use tokio::sync::mpsc;
use tracing::info;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 应用状态
#[derive(Debug, Default)]
pub struct AppState {
    /// 服务器是否正在运行
    pub is_running: bool,
    /// 连接的客户端数量
    pub client_count: u32,
    /// 服务器启动时间
    pub start_time: Option<std::time::SystemTime>,
}

/// Tauri集成的MCP服务器
#[derive(Clone)]
pub struct TauriMcpServer {
    /// 应用状态
    app_state: Arc<RwLock<AppState>>,
    /// 关闭信号发送器
    shutdown_tx: Option<Arc<tokio::sync::Mutex<Option<mpsc::UnboundedSender<()>>>>>,
}

#[server]
impl TauriMcpServer {
    /// 创建新的MCP服务器实例
    pub fn new() -> Self {
        Self {
            app_state: Arc::new(RwLock::new(AppState::default())),
            shutdown_tx: None,
        }
    }

    /// 获取服务器状态
    #[tool("获取MCP服务器状态信息")]
    pub async fn get_server_status(&self, ctx: Context) -> McpResult<String> {
        ctx.info("获取服务器状态").await?;

        let state = self.app_state.read().await;
        let uptime = if let Some(start_time) = state.start_time {
            format!("{:.2}秒", start_time.elapsed().unwrap_or_default().as_secs_f64())
        } else {
            "未知".to_string()
        };

        let status = serde_json::json!({
            "running": state.is_running,
            "clients": state.client_count,
            "uptime": uptime,
            "server_type": "Tauri集成MCP服务器"
        });

        Ok(status.to_string())
    }

    /// 获取应用信息
    #[tool("获取Tauri应用信息")]
    pub async fn get_app_info(&self, ctx: Context) -> McpResult<String> {
        ctx.info("获取应用信息").await?;
        
        let app_info = serde_json::json!({
            "name": "tauri-app",
            "version": "0.1.0",
            "framework": "Tauri 2.0",
            "frontend": "React",
            "mcp_integration": "TurboMCP 1.0"
        });

        Ok(app_info.to_string())
    }

    /// 执行系统命令（安全限制）
    #[tool("执行安全的系统命令")]
    pub async fn execute_safe_command(
        &self,
        ctx: Context,
        command: String
    ) -> McpResult<String> {
        ctx.info(&format!("执行命令: {}", command)).await?;

        // 安全命令白名单
        let safe_commands = vec!["ls", "pwd", "date", "whoami", "uname"];
        let cmd_parts: Vec<&str> = command.split_whitespace().collect();

        if cmd_parts.is_empty() {
            return Err(McpError::invalid_request("命令不能为空"));
        }

        let base_cmd = cmd_parts[0];
        if !safe_commands.contains(&base_cmd) {
            return Err(McpError::invalid_request(
                format!("不允许执行的命令: {}", base_cmd)
            ));
        }

        // 执行命令
        match tokio::process::Command::new(base_cmd)
            .args(&cmd_parts[1..])
            .output()
            .await
        {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let stderr = String::from_utf8_lossy(&output.stderr);

                if output.status.success() {
                    Ok(stdout.to_string())
                } else {
                    Err(McpError::internal(
                        format!("命令执行失败: {}", stderr)
                    ))
                }
            }
            Err(e) => Err(McpError::internal(
                format!("命令执行错误: {}", e)
            ))
        }
    }

    /// 读取文件内容（安全限制）
    #[resource("file://{path}")]
    pub async fn read_file(&self, path: String) -> McpResult<String> {
        // 安全检查：只允许读取项目目录下的文件
        if path.contains("..") || path.starts_with("/") {
            return Err(McpError::invalid_request(
                format!("不允许访问的路径: {}", path)
            ));
        }

        match tokio::fs::read_to_string(&path).await {
            Ok(content) => Ok(content),
            Err(e) => Err(McpError::internal(
                format!("读取文件失败: {}", e)
            ))
        }
    }

    /// 启动MCP服务器
    pub async fn start_server(&mut self) -> Result<mpsc::UnboundedReceiver<()>, Box<dyn std::error::Error>> {
        info!("启动TurboMCP服务器...");

        // 创建关闭信号通道
        let (shutdown_tx, shutdown_rx) = mpsc::unbounded_channel();
        self.shutdown_tx = Some(Arc::new(tokio::sync::Mutex::new(Some(shutdown_tx))));

        // 更新状态
        {
            let mut state = self.app_state.write().await;
            state.is_running = true;
            state.start_time = Some(std::time::SystemTime::now());
        }

        info!("TurboMCP服务器启动成功");
        Ok(shutdown_rx)
    }

    /// 停止MCP服务器
    pub async fn stop_server(&self) {
        info!("停止TurboMCP服务器...");

        if let Some(shutdown_tx_mutex) = &self.shutdown_tx {
            let mut tx_guard = shutdown_tx_mutex.lock().await;
            if let Some(tx) = tx_guard.take() {
                let _ = tx.send(());
            }
        }

        // 更新状态
        {
            let mut state = self.app_state.write().await;
            state.is_running = false;
            state.start_time = None;
        }

        info!("TurboMCP服务器已停止");
    }
}

impl Default for TauriMcpServer {
    fn default() -> Self {
        Self::new()
    }
}
