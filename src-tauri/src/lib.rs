// MCP服务器模块
mod mcp;

use mcp::TauriMcpServer;
use tokio::sync::RwLock;
use std::sync::Arc;
use tracing::{info, error};

// 全局MCP服务器实例
static MCP_SERVER: once_cell::sync::Lazy<Arc<RwLock<Option<TauriMcpServer>>>> =
    once_cell::sync::Lazy::new(|| Arc::new(RwLock::new(None)));

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

/// 启动MCP服务器
#[tauri::command]
async fn start_mcp_server() -> Result<String, String> {
    info!("通过Tauri命令启动MCP服务器");

    let server_lock = MCP_SERVER.clone();
    let mut server_guard = server_lock.write().await;

    if server_guard.is_some() {
        return Ok("MCP服务器已经在运行".to_string());
    }

    let server = TauriMcpServer::new();
    *server_guard = Some(server);

    Ok("MCP服务器启动成功".to_string())
}

/// 停止MCP服务器
#[tauri::command]
async fn stop_mcp_server() -> Result<String, String> {
    info!("通过Tauri命令停止MCP服务器");

    let server_lock = MCP_SERVER.clone();
    let mut server_guard = server_lock.write().await;

    if let Some(server) = server_guard.take() {
        server.stop_server().await;
        Ok("MCP服务器已停止".to_string())
    } else {
        Ok("MCP服务器未运行".to_string())
    }
}

/// 获取MCP服务器状态
#[tauri::command]
async fn get_mcp_status() -> Result<String, String> {
    let server_lock = MCP_SERVER.clone();
    let server_guard = server_lock.read().await;

    if server_guard.is_some() {
        // 创建一个简单的状态响应，因为我们无法轻易创建Context
        Ok(r#"{"running": true, "message": "MCP服务器正在运行"}"#.to_string())
    } else {
        Ok(r#"{"running": false, "message": "MCP服务器未启动"}"#.to_string())
    }
}

/// 启动独立的MCP服务器（用于Claude Desktop集成）
pub async fn run_mcp_server_standalone() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("启动独立MCP服务器用于Claude Desktop集成");

    let server = TauriMcpServer::new();
    server.run_stdio().await?;

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 启动Tauri应用
    let app_result = tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            start_mcp_server,
            stop_mcp_server,
            get_mcp_status
        ])
        .setup(|app| {
            info!("Tauri应用启动中...");

            // 在后台启动MCP服务器
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                if let Err(e) = setup_mcp_server().await {
                    error!("MCP服务器设置失败: {}", e);
                }
            });

            Ok(())
        })
        .build(tauri::generate_context!());

    match app_result {
        Ok(app) => {
            info!("Tauri应用启动成功");
            app.run(|_app_handle, event| {
                if let tauri::RunEvent::ExitRequested { .. } = event {
                    info!("应用退出中，清理MCP服务器...");
                    // 在应用退出时清理MCP服务器
                    tauri::async_runtime::spawn(async {
                        cleanup_mcp_server().await;
                    });
                }
            });
        }
        Err(e) => {
            error!("Tauri应用启动失败: {}", e);
            panic!("error while running tauri application: {}", e);
        }
    }
}

/// 设置MCP服务器
async fn setup_mcp_server() -> Result<(), Box<dyn std::error::Error>> {
    info!("设置MCP服务器...");

    let server_lock = MCP_SERVER.clone();
    let mut server_guard = server_lock.write().await;

    if server_guard.is_none() {
        let mut server = TauriMcpServer::new();
        let _shutdown_rx = server.start_server().await?;
        *server_guard = Some(server);
        info!("MCP服务器设置完成");
    }

    Ok(())
}

/// 清理MCP服务器
async fn cleanup_mcp_server() {
    info!("清理MCP服务器...");

    let server_lock = MCP_SERVER.clone();
    let mut server_guard = server_lock.write().await;

    if let Some(server) = server_guard.take() {
        server.stop_server().await;
        info!("MCP服务器已清理");
    }
}
