import { useState, useEffect } from "react";
import reactLogo from "./assets/react.svg";
import { invoke } from "@tauri-apps/api/core";
import "./App.css";

function App() {
  const [greetMsg, setGreetMsg] = useState("");
  const [name, setName] = useState("");
  const [mcpStatus, setMcpStatus] = useState("");
  const [mcpRunning, setMcpRunning] = useState(false);
  const [loading, setLoading] = useState(false);

  async function greet() {
    // Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
    setGreetMsg(await invoke("greet", { name }));
  }

  // MCP服务器控制函数
  async function startMcpServer() {
    setLoading(true);
    try {
      const result = await invoke("start_mcp_server");
      setMcpStatus(result as string);
      await updateMcpStatus();
    } catch (error) {
      setMcpStatus(`启动失败: ${error}`);
    }
    setLoading(false);
  }

  async function stopMcpServer() {
    setLoading(true);
    try {
      const result = await invoke("stop_mcp_server");
      setMcpStatus(result as string);
      await updateMcpStatus();
    } catch (error) {
      setMcpStatus(`停止失败: ${error}`);
    }
    setLoading(false);
  }

  async function updateMcpStatus() {
    try {
      const status = await invoke("get_mcp_status");
      const statusObj = JSON.parse(status as string);
      setMcpRunning(statusObj.running || false);
      setMcpStatus(JSON.stringify(statusObj, null, 2));
    } catch (error) {
      setMcpStatus(`获取状态失败: ${error}`);
      setMcpRunning(false);
    }
  }

  // 组件加载时获取MCP状态
  useEffect(() => {
    updateMcpStatus();
  }, []);

  return (
    <main className="container">
      <h1>Welcome to Tauri + React + TurboMCP</h1>

      <div className="row">
        <a href="https://vite.dev" target="_blank">
          <img src="/vite.svg" className="logo vite" alt="Vite logo" />
        </a>
        <a href="https://tauri.app" target="_blank">
          <img src="/tauri.svg" className="logo tauri" alt="Tauri logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <p>Click on the Tauri, Vite, and React logos to learn more.</p>

      {/* 原有的问候功能 */}
      <form
        className="row"
        onSubmit={(e) => {
          e.preventDefault();
          greet();
        }}
      >
        <input
          id="greet-input"
          onChange={(e) => setName(e.currentTarget.value)}
          placeholder="Enter a name..."
        />
        <button type="submit">Greet</button>
      </form>
      <p>{greetMsg}</p>

      {/* MCP服务器控制面板 */}
      <div className="mcp-panel" style={{
        marginTop: '2rem',
        padding: '1rem',
        border: '1px solid #ccc',
        borderRadius: '8px',
        backgroundColor: '#f9f9f9'
      }}>
        <h2>TurboMCP 服务器控制</h2>

        <div className="mcp-controls" style={{ marginBottom: '1rem' }}>
          <button
            onClick={startMcpServer}
            disabled={loading || mcpRunning}
            style={{
              marginRight: '0.5rem',
              backgroundColor: mcpRunning ? '#28a745' : '#007bff',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              cursor: loading || mcpRunning ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '处理中...' : mcpRunning ? '✓ 已启动' : '启动 MCP 服务器'}
          </button>

          <button
            onClick={stopMcpServer}
            disabled={loading || !mcpRunning}
            style={{
              marginRight: '0.5rem',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              cursor: loading || !mcpRunning ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '处理中...' : '停止 MCP 服务器'}
          </button>

          <button
            onClick={updateMcpStatus}
            disabled={loading}
            style={{
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '刷新中...' : '刷新状态'}
          </button>
        </div>

        <div className="mcp-status">
          <h3>服务器状态:</h3>
          <pre style={{
            backgroundColor: '#f8f9fa',
            padding: '1rem',
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '0.9rem',
            border: '1px solid #dee2e6'
          }}>
            {mcpStatus || '正在获取状态...'}
          </pre>
        </div>

        <div className="mcp-info" style={{ marginTop: '1rem' }}>
          <h3>Claude Desktop 集成说明:</h3>
          <p>1. 构建独立的MCP服务器: <code>cargo build --bin mcp-server</code></p>
          <p>2. 在Claude Desktop配置中添加:</p>
          <pre style={{
            backgroundColor: '#f8f9fa',
            padding: '0.5rem',
            borderRadius: '4px',
            fontSize: '0.8rem',
            border: '1px solid #dee2e6'
          }}>
{`{
  "mcpServers": {
    "tauri-mcp-server": {
      "command": "./target/debug/mcp-server",
      "args": []
    }
  }
}`}
          </pre>
        </div>
      </div>
    </main>
  );
}

export default App;
